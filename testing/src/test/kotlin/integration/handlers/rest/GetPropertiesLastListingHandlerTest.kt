package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.listings.RentListingResponse
import com.keyway.adapters.dtos.listings.RentListingsResponse
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROP_IDS_PARAM_NAME
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.utils.DateUtils
import integration.handlers.utils.DataUtils
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import kong.unirest.GenericType
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.koin.test.inject
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

class GetPropertiesLastListingHandlerTest : BaseApplicationTest() {
    private val listingsRepository: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()

    companion object {
        @JvmStatic
        @BeforeAll
        fun initClock() = DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))
    }

    fun createEffective(listing: RentListing) =
        EffectiveRent(
            id = UUID.randomUUID().toString(),
            rentListingId = listing.id,
            concessionIds = listOf(),
            dateFrom = listing.dateFrom,
            dateTo = listing.dateTo,
            rent = listing.rent,
            rentDeposit = listing.rentDeposit,
            concessions = "",
            createdAt = DateUtils.now(),
            updateAt = DateUtils.now(),
            propertyId = listing.propertyId,
            type = listing.type,
            typeId = listing.typeId,
            recordSource = listing.recordSource,
            zipCode = listing.zipCode,
            msaCode = listing.msaCode,
            unitSquareFootage = listing.unitSquareFootage,
            bedroomsQuantity = listing.bedroomsQuantity,
            bathroomsQuantity = listing.bathroomsQuantity,
            floorPlan = listing.floorPlan,
        ).also(effectiveRentRepository::save)

    @Test
    fun `Should retrieve listings for a single property`() {
        // Given
        val propertiesList = "USTX-027626"
        val givenUrl = "${localUrl()}/multifamily/units"

        val elements = DataUtils.multipleListingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            listOf(
                RentListingsResponse(
                    propertyId = "USTX-027626",
                    units =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "104",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "141",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-08-03"),
                                listingTo = LocalDate.parse("2023-08-04"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = BigDecimal("600.00"),
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ).sortedBy { it.unitId },
                ),
            )

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                    ),
                ).asObject(object : GenericType<List<RentListingsResponse>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)

        val sortedActualResponse =
            result.body
                .map { it.copy(units = it.units.sortedBy { unit -> unit.unitId }) }
                .sortedBy { it.propertyId }

        assertEquals(expectedResponse.size, sortedActualResponse.size)
        assertEquals(expectedResponse, sortedActualResponse)
    }

    @Test
    fun `Should not retrieve listings because they dont have effective rent`() {
        // Given
        val propertiesList = "USTX-027626"
        val givenUrl = "${localUrl()}/multifamily/units"

        val elements = DataUtils.multipleListingsData
        elements.forEach {
            listingsRepository.save(it)
        }

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                    ),
                ).asObject(object : GenericType<List<RentListingsResponse>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(result.body?.size, 0)
    }

    @Test
    fun `Should retrieve listings for a given list of properties`() {
        // Given
        val propertiesList = "USTX-027626,USTX-027329,USTX-027777,USTX-022222"
        val givenUrl = "${localUrl()}/multifamily/units"

        val elements = DataUtils.multipleListingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            listOf(
                RentListingsResponse(
                    propertyId = "USTX-027626",
                    units =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "104",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "141",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-08-03"),
                                listingTo = LocalDate.parse("2023-08-04"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = BigDecimal("600.00"),
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ).sortedBy { it.unitId },
                ),
                RentListingsResponse(
                    propertyId = "USTX-027329",
                    units =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "289",
                                floorPlan = "A3",
                                askingRent = BigDecimal("999.00"),
                                listingFrom = LocalDate.parse("2023-10-05"),
                                listingTo = LocalDate.parse("2023-10-07"),
                                availableOn = null,
                                bedrooms = 3,
                                bathrooms = BigDecimal("2"),
                                deposit = BigDecimal("600.00"),
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ).sortedBy { it.unitId },
                ),
                RentListingsResponse(
                    propertyId = "USTX-027777",
                    units =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "225",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1093.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ).sortedBy { it.unitId },
                ),
                RentListingsResponse(
                    propertyId = "USTX-022222",
                    units =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "222",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1093.00"),
                                listingFrom = LocalDate.parse("2024-10-17"),
                                listingTo = LocalDate.parse("2024-10-17"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ).sortedBy { it.unitId },
                ),
            ).sortedBy { it.propertyId }

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                    ),
                ).asObject(object : GenericType<List<RentListingsResponse>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)

        val sortedActualResponse =
            result.body
                .map { it.copy(units = it.units.sortedBy { unit -> unit.unitId }) }
                .sortedBy { it.propertyId }

        assertEquals(expectedResponse.size, sortedActualResponse.size)
        assertEquals(expectedResponse, sortedActualResponse)
    }

    @Test
    fun `Should retrieve listings for a given list of properties with custom period`() {
        // Given
        val propertiesList = "USTX-027626,USTX-027329,USTX-027777,USTX-022222"
        val givenUrl = "${localUrl()}/multifamily/units"

        val elements = DataUtils.multipleListingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            listOf(
                RentListingsResponse(
                    propertyId = "USTX-027626",
                    units =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "104",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-09-29"),
                                listingTo = LocalDate.parse("2023-09-29"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
                RentListingsResponse(
                    propertyId = "USTX-027329",
                    units =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "289",
                                floorPlan = "A3",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-09-29"),
                                listingTo = LocalDate.parse("2023-09-29"),
                                availableOn = null,
                                bedrooms = 3,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to LocalDate.parse("2023-09-01"),
                        DATE_TO_PARAM_NAME to LocalDate.parse("2023-09-30"),
                    ),
                ).asObject(object : GenericType<List<RentListingsResponse>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        result.body.shouldContainExactlyInAnyOrder(expectedResponse)
    }

    @Test
    fun `Shouldn't retrieve listings because of property does not exist`() {
        // Given
        val propertiesList = "USTX-111111,USTX-011112"
        val givenUrl = "${localUrl()}/multifamily/units"

        val elements = DataUtils.multipleListingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                    ),
                ).asObject(object : GenericType<List<RentListingsResponse>>() {})

        // Then
        assertNotNull(result)
        assertFalse(result.isSuccess)
        assertEquals(result.status, HttpStatus.NOT_FOUND)
    }
}
