package com.keyway.adapters.converters.dto

import com.keyway.adapters.dtos.listings.EffectiveRentResponse
import com.keyway.adapters.dtos.listings.PropertiesListingDataInput
import com.keyway.adapters.dtos.listings.RentListingResponse
import com.keyway.adapters.handlers.utils.HandlersUtils.MAX_PROPERTIES_ALLOWED
import com.keyway.adapters.handlers.utils.HandlersUtils.MIN_PROPERTIES_ALLOWED
import com.keyway.adapters.handlers.utils.HandlersUtils.PROPERTIES_DELIMITER
import com.keyway.core.dto.listings.input.GetPropertiesListingsInput
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.utils.DateUtils
import com.keyway.core.utils.DateUtils.isBetween
import com.keyway.core.utils.ListingUtils
import java.time.LocalDate

object PropertiesListingConverter {
    fun toGetPropertiesListingsInput(propertiesListingDataInput: PropertiesListingDataInput): GetPropertiesListingsInput =
        GetPropertiesListingsInput(
            propertyIds = getValidPropertySet(propertiesListingDataInput.propertiesIds),
            dateFrom = propertiesListingDataInput.dateFrom,
            dateTo = propertiesListingDataInput.dateTo,
            bedrooms = propertiesListingDataInput.bedrooms,
            bathrooms = propertiesListingDataInput.bathrooms,
        )

    private fun getValidPropertySet(propIdList: Set<String>): Set<String> {
        val result =
            propIdList
                .map { it.trim().split(PROPERTIES_DELIMITER) }
                .flatten()
                .filter { it.isNotBlank() }

        require(result.size <= MAX_PROPERTIES_ALLOWED) { "The maximum amount of properties allowed is $MAX_PROPERTIES_ALLOWED" }
        require(result.size >= MIN_PROPERTIES_ALLOWED) { "The minimum amount of properties allowed is $MIN_PROPERTIES_ALLOWED" }

        return result.toSet()
    }

    fun toResponse(
        entity: RentListing,
        effectiveRents: List<EffectiveRent>,
    ): RentListingResponse =
        RentListingResponse(
            rentListingType = entity.type,
            unitId = getUnitId(entity),
            floorPlan = getFloorPlan(entity),
            askingRent = entity.rent.value,
            deposit = entity.rentDeposit?.value,
            squareFootage = entity.unitSquareFootage,
            listingFrom = entity.dateFrom,
            listingTo = entity.dateTo,
            active = isActive(entity.dateTo),
            availableOn = calculateAvailableOnDate(entity.availableIn, entity.dateTo),
            bedrooms = entity.bedroomsQuantity,
            bathrooms = entity.bathroomsQuantity,
            effectiveRents = effectiveRents.map { it.toResponse() },
        )

    private fun calculateAvailableOnDate(
        availableIn: LocalDate?,
        dateTo: LocalDate,
    ): LocalDate? =
        when {
            availableIn != null && availableIn > DateUtils.now() -> availableIn
            availableIn == null && isActive(dateTo) -> DateUtils.now()
            else -> null
        }

    private fun isActive(dateTo: LocalDate): Boolean = dateTo.isBetween(DateUtils.now<LocalDate>().minusDays(ListingUtils.ACTIVE_AMOUNT_DAYS), DateUtils.now<LocalDate>())

    private fun getUnitId(entity: RentListing): String? =
        if (entity.type == RentListingType.UNIT) {
            entity.typeId
        } else {
            null
        }

    private fun getFloorPlan(entity: RentListing): String? =
        if (entity.type == RentListingType.FLOOR_PLAN) {
            entity.typeId
        } else {
            entity.floorPlan
        }

    private fun EffectiveRent.toResponse(): EffectiveRentResponse =
        EffectiveRentResponse(
            dateFrom = this.dateFrom,
            dateTo = this.dateTo,
            rent = this.rent.value,
            rentDeposit = this.rentDeposit?.value,
        )
}
