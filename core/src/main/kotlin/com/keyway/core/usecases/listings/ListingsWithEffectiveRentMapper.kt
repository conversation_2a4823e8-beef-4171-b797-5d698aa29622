package com.keyway.core.usecases.listings

import com.keyway.core.dto.listings.output.RentListingWithEffectiveRentOutput
import com.keyway.core.entities.RentListing
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.utils.distributeEvenly
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

class ListingsWithEffectiveRentMapper(
    private val effectiveRentRepository: EffectiveRentRepository,
) {
    fun mapByProperty(listings: List<RentListing>): Map<String, List<RentListingWithEffectiveRentOutput>> = mapToOutput(listings).groupBy { it.rentListing.propertyId }

    fun mapToOutput(listings: List<RentListing>): List<RentListingWithEffectiveRentOutput> {
        val effectiveRents =
            listings
                .map { it.id }
                .distributeEvenly(20)
                .let { partitionedIds ->
                    runBlocking {
                        withContext(Dispatchers.IO) {
                            val deferredResults =
                                partitionedIds.map { ids ->
                                    async {
                                        effectiveRentRepository
                                            .getByListingIds(
                                                listingIds = ids,
                                                dateFrom = null,
                                                dateTo = null,
                                            )
                                    }
                                }
                            deferredResults.awaitAll().flatten()
                        }
                    }
                }.groupBy { it.rentListingId }

        return listings.mapNotNull {
            RentListingWithEffectiveRentOutput(
                rentListing = it,
                effectiveRents = effectiveRents.getOrDefault(it.id, emptyList()),
            ).takeIf { result -> result.effectiveRents.isNotEmpty() }
        }
    }
}
