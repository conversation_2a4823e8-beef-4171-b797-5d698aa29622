package com.keyway.core.service.listing

import com.keyway.core.dto.listings.input.SaveRentGroupedListingInput
import com.keyway.core.dto.listings.input.UnitRecordsData
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.conciliation.ConciliationEffective
import com.keyway.core.entities.conciliation.ConciliationListing
import com.keyway.core.utils.DateUtils
import com.keyway.core.utils.DateUtils.isBetween
import com.keyway.core.utils.IdGenerator
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

class GroupedListingService(
    private val idGenerator: IdGenerator,
) {
    private fun buildExistingListingsMap(
        existingListings: List<RentListing>,
        effectiveRentListings: List<EffectiveRent>,
    ): Map<LocalDate, ConciliationListing> {
        val effectiveRentMap = effectiveRentListings.groupBy { it.rentListingId }

        return existingListings
            .flatMap { listing ->
                DateUtils.generateDateRange(listing.dateFrom, listing.dateTo).map { date ->
                    date to
                        createListingData(
                            listing,
                            date,
                            effectiveRentMap[listing.id]?.firstOrNull { date.isBetween(it.dateFrom, it.dateTo) },
                        )
                }
            }.toMap()
    }

    private fun getLastThreeAlphanumeric(input: String): String {
        // Filter only letters and numbers, then take the last 3
        val alphanumeric = input.filter { it.isLetterOrDigit() }
        return if (alphanumeric.length >= 3) {
            alphanumeric.takeLast(3)
        } else {
            alphanumeric // Return all available characters if less than 3
        }
    }

    fun buildId(
        propertyId: String,
        typeId: String,
    ) = "${propertyId.split("-").last()}-${getLastThreeAlphanumeric(typeId)}-${idGenerator.invoke()}"

    fun processGroupedListing(
        input: SaveRentGroupedListingInput,
        existingListings: List<RentListing>,
        effectiveRentListings: List<EffectiveRent>,
        incomingSortedRecords: List<UnitRecordsData>,
    ): Pair<List<RentListing>, List<EffectiveRent>> {
        val existingListingsMap = buildExistingListingsMap(existingListings, effectiveRentListings)
        val incomingListingsMap = buildIncomingListingsMap(incomingSortedRecords, input.propertyId, input.typeId)

        val (lowerLimit, higherLimit) = calculateSequenceLimits(existingListings, incomingSortedRecords)

        val conciliatedMap = conciliateListings(lowerLimit, higherLimit, existingListingsMap, incomingListingsMap)

        return generateListings(conciliatedMap, input)
    }

    private fun generateListings(
        conciliatedMap: Map<LocalDate, ConciliationListing>,
        input: SaveRentGroupedListingInput,
    ): Pair<List<RentListing>, List<EffectiveRent>> {
        val effectiveRents = mutableListOf<EffectiveRent>()
        return conciliatedMap.values
            .groupBy { it.id }
            .map { (id, listings) ->
                val conciliationElement = generateConciliationElement(listings)

                val maxDate = listings.maxBy { it.date }.date

                createRentListing(id = id!!, dateFrom = conciliationElement.date, dateTo = maxDate, input = input, conciliationElement = conciliationElement)
                    .also {
                        effectiveRents.addAll(buildEffectiveRentList(it, listings))
                    }
            } to effectiveRents
    }

    private fun buildEffectiveRentList(
        rentListing: RentListing,
        listings: List<ConciliationListing>,
    ): List<EffectiveRent> =
        listings
            .groupBy { it.effective.id }
            .map { (id, effectiveList) ->
                val effectiveData = effectiveList.first().effective
                EffectiveRent.build(
                    id = id!!,
                    listing = rentListing,
                    concessionIds = emptyList(),
                    dateFrom = effectiveList.minBy { it.date }.date,
                    dateTo = effectiveList.maxBy { it.date }.date,
                    rent = getRentValue(effectiveData.effectiveRent, rentListing.rent),
                    rentDeposit = getDepositValue(effectiveData.effectiveDeposit, rentListing.rentDeposit),
                    concessions = effectiveData.concessions ?: "",
                    createdAt = effectiveData.createdAt,
                    updateAt = DateUtils.now(),
                )
            }

    private fun generateConciliationElement(listings: List<ConciliationListing>): ConciliationListing {
        val listingElement = listings.minBy { it.date }

        val availableIn: LocalDate? =
            listings
                .mapNotNull { it.availableIn }
                .maxOrNull()

        val maxDeposit: Money? =
            listings
                .mapNotNull { it.rentDeposit }
                .maxOrNull()

        val maxEffectiveDeposit: Money? =
            listings
                .mapNotNull { it.effective.effectiveDeposit }
                .maxOrNull()

        return listingElement.copy(
            availableIn = availableIn,
            rentDeposit = maxDeposit,
            conciliationEffective = listingElement.effective.copy(effectiveDeposit = maxEffectiveDeposit),
        )
    }

    private fun calculateSequenceLimits(
        existingListings: List<RentListing>,
        sortedRecords: List<UnitRecordsData>,
    ): Pair<LocalDate, LocalDate> =
        listOfNotNull(
            existingListings.firstOrNull()?.dateFrom,
            existingListings.lastOrNull()?.dateTo,
            sortedRecords.first().recordDate,
            sortedRecords.last().recordDate,
        ).let { it.min() to it.max() }

    private fun createListingData(
        listing: RentListing,
        date: LocalDate,
        effectiveRent: EffectiveRent?,
    ): ConciliationListing =
        ConciliationListing(
            id = listing.id,
            propertyId = listing.propertyId,
            typeId = listing.typeId,
            date = date,
            recordSource = listing.recordSource,
            rent = listing.rent,
            rentDeposit = listing.rentDeposit,
            effective =
                ConciliationEffective(
                    id = effectiveRent?.id,
                    recordDate = date,
                    concessions = effectiveRent?.concessions,
                    effectiveRent = effectiveRent?.rent ?: Money.zero(),
                    effectiveDeposit = effectiveRent?.rentDeposit ?: listing.rentDeposit,
                    createdAt = effectiveRent?.createdAt ?: DateUtils.now(),
                ),
            availableIn = listing.availableIn,
            createdAt = listing.createdAt,
        )

    private fun conciliateListings(
        lowerLimit: LocalDate,
        higherLimit: LocalDate,
        existingListingsMap: Map<LocalDate, ConciliationListing>,
        incomingListingsMap: Map<LocalDate, ConciliationListing>,
    ): Map<LocalDate, ConciliationListing> {
        val conciliatedListingsMap = mutableMapOf<LocalDate, ConciliationListing>()
        var lastProcessedListing: ConciliationListing? = null
        DateUtils
            .generateDateRange(lowerLimit, higherLimit)
            .forEach { date ->
                val existingListing = existingListingsMap[date]
                val incomingListing = incomingListingsMap[date]

                val conciliatedListing = selectListing(existingListing, incomingListing, lastProcessedListing)

                if (conciliatedListing != null) {
                    conciliatedListingsMap[date] = conciliatedListing
                    lastProcessedListing = conciliatedListing
                }
            }
        return conciliatedListingsMap
    }

    private fun selectListing(
        existingListing: ConciliationListing?,
        incomingListing: ConciliationListing?,
        lastProcessedListing: ConciliationListing?,
    ): ConciliationListing? =
        when {
            existingListing != null && incomingListing != null -> createConciliationListing(existingListing, incomingListing, lastProcessedListing)
            incomingListing != null ->
                incomingListing.copy(
                    id = getListingId(lastProcessedListing, incomingListing, false),
                    createdAt = getCreatedAt(lastProcessedListing, incomingListing, false),
                    conciliationEffective =
                        incomingListing.effective.copy(
                            id =
                                getEffectiveId(
                                    lastProcessedListing?.effective,
                                    incomingListing.effective,
                                    false,
                                    incomingListing.propertyId,
                                    incomingListing.typeId,
                                ),
                        ),
                )
            existingListing != null ->
                existingListing.copy(
                    id = getListingId(lastProcessedListing, existingListing, true),
                    createdAt = getCreatedAt(lastProcessedListing, existingListing, true),
                    conciliationEffective =
                        existingListing.effective.copy(
                            id =
                                getEffectiveId(
                                    lastProcessedListing?.effective,
                                    existingListing.effective,
                                    true,
                                    existingListing.propertyId,
                                    existingListing.typeId,
                                ),
                        ),
                )
            else -> null
        }

    private fun getListingId(
        lastProcessedListing: ConciliationListing?,
        listing: ConciliationListing,
        existing: Boolean,
    ): String =
        when {
            lastProcessedListing?.id == null && listing.id != null -> listing.id
            lastProcessedListing?.id != null && lastProcessedListing == listing -> lastProcessedListing.id
            existing && lastProcessedListing?.id != null && listing.id != null && lastProcessedListing != listing -> listing.id
            else -> buildId(listing.propertyId, listing.typeId)
        }

    private fun getCreatedAt(
        lastProcessedListing: ConciliationListing?,
        listing: ConciliationListing,
        existing: Boolean,
    ): OffsetDateTime =
        when {
            lastProcessedListing?.id == null && listing.id != null -> listing.createdAt
            lastProcessedListing?.id != null && lastProcessedListing == listing -> lastProcessedListing.createdAt
            existing && lastProcessedListing?.id != null && listing.id != null && lastProcessedListing != listing -> listing.createdAt
            else -> DateUtils.now()
        }

    private fun getEffectiveCreatedAt(
        lastProcessedEffective: ConciliationEffective?,
        effective: ConciliationEffective,
        existing: Boolean,
    ): OffsetDateTime =
        when {
            lastProcessedEffective?.id == null && effective.id != null -> effective.createdAt
            lastProcessedEffective?.id != null && lastProcessedEffective == effective -> effective.createdAt
            existing && lastProcessedEffective?.id != null && effective.id != null && lastProcessedEffective != effective -> effective.createdAt
            else -> DateUtils.now()
        }

    private fun getEffectiveId(
        lastProcessedEffective: ConciliationEffective?,
        effective: ConciliationEffective,
        existing: Boolean,
        propertyId: String,
        typeId: String,
    ): String =
        when {
            lastProcessedEffective?.id == null && effective.id != null -> effective.id
            lastProcessedEffective?.id != null && lastProcessedEffective == effective -> lastProcessedEffective.id
            existing && lastProcessedEffective?.id != null && effective.id != null && lastProcessedEffective != effective -> effective.id
            else -> buildId(propertyId, typeId)
        }

    private fun createConciliationListing(
        existingElement: ConciliationListing,
        incomingElement: ConciliationListing,
        lastProcessedListing: ConciliationListing?,
    ): ConciliationListing =
        when {
            lastProcessedListing == incomingElement ->
                incomingElement.copy(
                    id = getListingId(lastProcessedListing, incomingElement, false),
                    createdAt = getCreatedAt(lastProcessedListing, incomingElement, false),
                )
            existingElement != incomingElement ->
                incomingElement.copy(
                    id = getListingId(lastProcessedListing, incomingElement, false),
                    createdAt = getCreatedAt(lastProcessedListing, incomingElement, false),
                )
            else -> existingElement
        }.copy(
            conciliationEffective =
                when {
                    lastProcessedListing?.effective == incomingElement.effective ->
                        lastProcessedListing.effective.copy(
                            id =
                                getEffectiveId(
                                    lastProcessedListing.effective,
                                    incomingElement.effective,
                                    true,
                                    incomingElement.propertyId,
                                    incomingElement.typeId,
                                ),
                            createdAt =
                                getEffectiveCreatedAt(
                                    lastProcessedListing?.effective,
                                    incomingElement.effective,
                                    true,
                                ),
                        )
                    existingElement.effective != incomingElement.effective ->
                        incomingElement.effective.copy(
                            id =
                                getEffectiveId(
                                    lastProcessedListing?.effective,
                                    incomingElement.effective,
                                    false,
                                    incomingElement.propertyId,
                                    incomingElement.typeId,
                                ),
                            createdAt =
                                getEffectiveCreatedAt(
                                    lastProcessedListing?.effective,
                                    incomingElement.effective,
                                    false,
                                ),
                        )
                    else -> existingElement.effective
                },
        )

    private fun buildIncomingListingsMap(
        sortedRecords: List<UnitRecordsData>,
        propertyId: String,
        typeId: String,
    ) = sortedRecords.associate { listingData ->
        listingData.recordDate to
            ConciliationListing(
                id = null,
                date = listingData.recordDate,
                recordSource = listingData.recordSource,
                rent = Money.of(listingData.rent),
                rentDeposit = listingData.rentDeposit?.let { deposit -> Money.of(deposit) },
                effective =
                    ConciliationEffective(
                        id = null,
                        recordDate = listingData.recordDate,
                        concessions = listingData.concessions,
                        effectiveRent = Money.of(listingData.effectiveRent),
                        effectiveDeposit = listingData.effectiveRentDeposit?.let { Money.of(it) },
                        createdAt = DateUtils.now(),
                    ),
                availableIn = listingData.availableIn,
                createdAt = DateUtils.now(),
                propertyId = propertyId,
                typeId = typeId,
            )
    }

    private fun createRentListing(
        id: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        input: SaveRentGroupedListingInput,
        conciliationElement: ConciliationListing,
    ) = RentListing(
        id = id,
        propertyId = input.propertyId,
        type = input.type,
        typeId = input.typeId,
        dateFrom = dateFrom,
        dateTo = dateTo,
        recordSource = conciliationElement.recordSource,
        zipCode = input.zipCode,
        msaCode = input.msaCode,
        unitSquareFootage = input.unitSquareFootage,
        bedroomsQuantity = input.bedroomsQuantity,
        bathroomsQuantity = input.bathroomsQuantity,
        floorPlan = input.floorPlan,
        availableIn = conciliationElement.availableIn?.takeIf { it.isAfter(DateUtils.now()) },
        rent = conciliationElement.rent,
        rentDeposit = conciliationElement.rentDeposit,
        createdAt = conciliationElement.createdAt,
        updateAt = DateUtils.now(),
    )

    private fun getRentValue(
        effectiveValue: Money?,
        inputValue: Money,
    ): Money =
        effectiveValue?.takeIf { it.value > BigDecimal.ZERO }
            ?: inputValue

    private fun getDepositValue(
        effectiveValue: Money?,
        inputValue: Money?,
    ): Money? =
        effectiveValue?.takeIf { it.value > BigDecimal.ZERO }
            ?: inputValue
}
